from tqdm import tqdm # type: ignore
import json
import sys

from preProcessPDF import extract_page, get_pdf_reader_and_metadata
from model import generate, evaluate
from prompts import answer_sheet_prompt, rubric_prompt, evaluation_prompt, create_rubric_prompt

def process_all_pages(pdf_path, file_type):
    """
    Processes all pages in a PDF and returns an array of Document objects.
    """
    reader, metadata = get_pdf_reader_and_metadata(pdf_path)
    documents = []
    
    for page_number in tqdm(range(len(reader.pages)), desc=f"Processing {file_type} pages"):
        page_metadata = metadata.copy()
        page_metadata['page'] = page_number + 1
        
        b64_page = extract_page(reader, page_number)
        document_page = generate(b64_page, answer_sheet_prompt if file_type == "answer_sheet" else rubric_prompt if file_type == "rubric" else create_rubric_prompt, page_metadata)
        documents.append(document_page)
        
    return documents

def combine_and_evaluate(answer_sheet_path, rubric_path):
    
    answer_sheet_data = ""
    rubric_data = ""
    
    with open(answer_sheet_path, 'r') as jsonl_file:
        for line in jsonl_file:
            try:
                data = json.loads(line)
                answer_sheet_data += data['page_content'] + "\n\n"
            except json.JSONDecodeError as e:
                print(f"Error parsing JSONL line in answer sheet: {str(e)}", file=sys.stderr)
    
    with open(rubric_path, 'r') as jsonl_file:
        for line in jsonl_file:
            try:
                data = json.loads(line)
                rubric_data += data['page_content'] + "\n\n"
            except json.JSONDecodeError as e:
                print(f"Error parsing JSONL line in rubric: {str(e)}", file=sys.stderr)

    with open('answer_sheet_data.md', 'w') as md_file:
        md_file.write(answer_sheet_data)

    with open('rubric_data.md', 'w') as md_file:
        md_file.write(rubric_data)

    print("Answer sheet data extracted successfully", file=sys.stderr)
    print("Rubric data extracted successfully", file=sys.stderr)
    
    evaluation_result = evaluate(answer_sheet_data, rubric_data, evaluation_prompt)
    
    return evaluation_result
    
def create_rubric(created_rubric_path):
    """
    Creates a rubric for a question paper.
    """
    

    rubric_data = ""
    with open(created_rubric_path, 'r') as jsonl_file:
        for line in jsonl_file:
            try:
                data = json.loads(line)
                rubric_data += data['page_content'] + "\n\n"
            except json.JSONDecodeError as e:
                print(f"Error parsing JSONL line in created rubric: {str(e)}", file=sys.stderr)

    with open('created_rubric_data.md', 'w') as md_file:
        md_file.write(rubric_data)

    print("Created rubric data extracted successfully", file=sys.stderr)

    return rubric_data

