import json
import boto3
import sys
import os

# For local testing, add layer path
if os.environ.get('AWS_SAM_LOCAL'):
    sys.path.append('/opt/python')
else:
    # In AWS Lambda, layers are automatically in /opt/python
    pass

from aegisGrader import run_grader_with_pdf_bytes

s3_client = boto3.client('s3')

def process_files(data):
    answer_sheet_keys = []
    question_paper_key = None
    rubric_key = None

    for file in data['files']:
        purpose = file.get('filePurpose')
        key = file.get('key')
        if purpose == 'answer_sheet':
            answer_sheet_keys.append(key)
        elif purpose == 'question_paper':
            question_paper_key = key
        elif purpose == 'rubric':
            rubric_key = key

    return {
        "answer_sheet_keys": answer_sheet_keys,
        "question_paper_key": question_paper_key,
        "rubric_key": rubric_key
    }

def download_pdf(bucket, key):
    response = s3_client.get_object(Bucket=bucket, Key=key)
    pdf_bytes = response['Body'].read()
    return pdf_bytes

def lambda_handler(event, context):
    records = event["Records"]
    print("Event => ", event)
    for record in records:
        body = json.loads(record["body"])
        s3 = body["Records"][0]["s3"]
        bucket = s3["bucket"]["name"]
        key = s3["object"]["key"]

        # Fetch and parse the manifest JSON from S3
        response = s3_client.get_object(Bucket=bucket, Key=key)
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)

        # Get the keys for the PDFs
        result = process_files(data)
        answer_sheet_keys = result["answer_sheet_keys"]
        question_paper_key = result["question_paper_key"]
        rubric_key = result["rubric_key"]

        # Download PDFs and store in memory
        pdfs = {}

        if question_paper_key:
            pdfs['question_paper'] = download_pdf(bucket, question_paper_key)
        if rubric_key:
            pdfs['rubric'] = download_pdf(bucket, rubric_key)
        pdfs['answer_sheets'] = []
        for ans_key in answer_sheet_keys:
            pdfs['answer_sheets'].append(download_pdf(bucket, ans_key))

        evaluated_results = run_grader_with_pdf_bytes(
            rubric_pdf_bytes=pdfs.get('rubric'),
            answer_sheet_pdf_bytes_list=pdfs.get('answer_sheets', []),
            question_paper_pdf_bytes=pdfs.get('question_paper')
        )

        with open('evaluation_results.json', 'w') as f:
            json.dump(evaluated_results, f) 
        return {"statusCode": 200, "body": json.dumps(evaluated_results)}
        # Now 'pdfs' dict contains all the PDF files as bytes, ready to send or process
        # print(f"Downloaded PDFs: {list(pdfs.keys())}")

    return {"statusCode": 200}
